'use client';

import React, { useState, useEffect } from 'react';
import { 
  MessageSquare, 
  Users, 
  TrendingUp, 
  Clock, 
  Eye,
  // Filter,
  Search,
  RefreshCw
} from 'lucide-react';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import Table from '@/components/ui/Table';
import { api } from '@/lib/api';
import { useAuthStore } from '@/store/auth';
import { Conversation, Member, Trainer } from '@/types';
import { formatDate, formatTime } from '@/lib/utils';

const MonitoringPage: React.FC = () => {
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [members, setMembers] = useState<Member[]>([]);
  const [trainers, setTrainers] = useState<Trainer[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [trainerFilter, setTrainerFilter] = useState<string>('all');
  const [dateFilter, setDateFilter] = useState<string>('today');
  const [autoRefresh, setAutoRefresh] = useState(true);
  
  const { user } = useAuthStore();

  useEffect(() => {
    fetchData();
    fetchTrainers();
    
    // Set up auto-refresh
    let interval: NodeJS.Timeout;
    if (autoRefresh) {
      interval = setInterval(fetchData, 5000); // Refresh every 5 seconds
    }
    
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [autoRefresh]);

  const fetchData = async () => {
    try {
      setLoading(true);
      const [conversationsRes, membersRes] = await Promise.all([
        api.monitoring.getConversations(getDateFilter()),
        api.members.getAll()
      ]);
      
      setConversations(conversationsRes.data);
      setMembers(membersRes.data);
    } catch (error) {
      console.error('Error fetching monitoring data:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchTrainers = async () => {
    try {
      const response = await api.trainers.getAll();
      setTrainers(response.data);
    } catch (error) {
      console.error('Error fetching trainers:', error);
    }
  };

  const getDateFilter = () => {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    
    switch (dateFilter) {
      case 'today':
        return { dateFrom: today.toISOString() };
      case 'week':
        const weekAgo = new Date(today);
        weekAgo.setDate(weekAgo.getDate() - 7);
        return { dateFrom: weekAgo.toISOString() };
      case 'month':
        const monthAgo = new Date(today);
        monthAgo.setMonth(monthAgo.getMonth() - 1);
        return { dateFrom: monthAgo.toISOString() };
      default:
        return {};
    }
  };

  const handleRefresh = () => {
    fetchData();
  };

  // Filter conversations
  const filteredConversations = conversations.filter(conv => {
    const matchesSearch = conv.message.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         conv.member?.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         conv.trainer?.name.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesTrainer = trainerFilter === 'all' || conv.trainerId.toString() === trainerFilter;
    
    return matchesSearch && matchesTrainer;
  });

  // Calculate stats
  const stats = {
    totalConversations: conversations.length,
    activeMembers: [...new Set(conversations.map(c => c.memberId))].length,
    averageScore: conversations.filter(c => c.aiScore).reduce((sum, c) => sum + (c.aiScore || 0), 0) / conversations.filter(c => c.aiScore).length || 0,
    recentActivity: conversations.filter(c => {
      const created = new Date(c.created_at);
      const hourAgo = new Date();
      hourAgo.setHours(hourAgo.getHours() - 1);
      return created > hourAgo;
    }).length
  };

  const columns = [
    {
      key: 'created_at' as keyof Conversation,
      label: 'Time',
      sortable: true,
      render: (value: string) => (
        <div className="text-sm">
          <div className="font-medium">{formatTime(value)}</div>
          <div className="text-gray-500">{formatDate(value)}</div>
        </div>
      )
    },
    {
      key: 'member' as keyof Conversation,
      label: 'Member',
      render: (value: Member) => (
        <div className="flex items-center">
          <div className="h-6 w-6 rounded-full bg-blue-100 flex items-center justify-center mr-2">
            <span className="text-xs font-medium text-blue-600">
              {value?.name?.charAt(0).toUpperCase() || 'U'}
            </span>
          </div>
          <span className="text-sm font-medium">{value?.name || 'Unknown'}</span>
        </div>
      )
    },
    {
      key: 'trainer' as keyof Conversation,
      label: 'Trainer',
      render: (value: Trainer) => (
        <div className="flex items-center">
          <div className="h-6 w-6 rounded-full bg-green-100 flex items-center justify-center mr-2">
            <span className="text-xs font-medium text-green-600">
              {value?.name?.charAt(0).toUpperCase() || 'T'}
            </span>
          </div>
          <span className="text-sm font-medium">{value?.name || 'Unknown'}</span>
        </div>
      )
    },
    {
      key: 'messageFrom' as keyof Conversation,
      label: 'From',
      render: (value: string) => (
        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
          value === 'member' ? 'bg-blue-100 text-blue-800' : 'bg-purple-100 text-purple-800'
        }`}>
          {value === 'member' ? 'Member' : 'AI'}
        </span>
      )
    },
    {
      key: 'message' as keyof Conversation,
      label: 'Message',
      render: (value: string) => (
        <div className="max-w-xs">
          <p className="text-sm text-gray-900 truncate">{value}</p>
        </div>
      )
    },
    {
      key: 'aiScore' as keyof Conversation,
      label: 'Score',
      render: (value: number) => value ? (
        <div className="flex items-center">
          <div className={`w-2 h-2 rounded-full mr-2 ${
            value >= 8 ? 'bg-green-500' : value >= 6 ? 'bg-yellow-500' : 'bg-red-500'
          }`}></div>
          <span className="text-sm font-medium">{value.toFixed(1)}</span>
        </div>
      ) : (
        <span className="text-gray-400">-</span>
      )
    },
    {
      key: 'id' as keyof Conversation,
      label: 'Actions',
      render: (value: number) => (
        <Button variant="ghost" size="sm">
          <Eye className="h-4 w-4" />
        </Button>
      )
    }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Monitoring</h1>
          <p className="text-gray-600">Real-time conversation tracking and analytics</p>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant={autoRefresh ? 'primary' : 'outline'}
            size="sm"
            onClick={() => setAutoRefresh(!autoRefresh)}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${autoRefresh ? 'animate-spin' : ''}`} />
            Auto Refresh
          </Button>
          <Button variant="outline" size="sm" onClick={handleRefresh}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <MessageSquare className="h-8 w-8 text-blue-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Conversations</p>
              <p className="text-2xl font-bold text-gray-900">{stats.totalConversations}</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <Users className="h-8 w-8 text-green-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Active Members</p>
              <p className="text-2xl font-bold text-gray-900">{stats.activeMembers}</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <TrendingUp className="h-8 w-8 text-purple-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Average Score</p>
              <p className="text-2xl font-bold text-gray-900">
                {stats.averageScore ? stats.averageScore.toFixed(1) : '0.0'}
              </p>
            </div>
          </div>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <Clock className="h-8 w-8 text-orange-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Recent Activity</p>
              <p className="text-2xl font-bold text-gray-900">{stats.recentActivity}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1">
            <Input
              placeholder="Search conversations..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              leftIcon={<Search className="h-4 w-4" />}
            />
          </div>
          
          <div className="md:w-48">
            <select
              value={trainerFilter}
              onChange={(e) => setTrainerFilter(e.target.value)}
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
            >
              <option value="all">All Trainers</option>
              {trainers.map(trainer => (
                <option key={trainer.id} value={trainer.id.toString()}>
                  {trainer.name}
                </option>
              ))}
            </select>
          </div>
          
          <div className="md:w-48">
            <select
              value={dateFilter}
              onChange={(e) => setDateFilter(e.target.value)}
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
            >
              <option value="today">Today</option>
              <option value="week">This Week</option>
              <option value="month">This Month</option>
              <option value="all">All Time</option>
            </select>
          </div>
        </div>
      </div>

      {/* Live Activity Indicator */}
      {autoRefresh && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse mr-3"></div>
            <span className="text-sm font-medium text-green-800">
              Live monitoring active - Updates every 5 seconds
            </span>
          </div>
        </div>
      )}

      {/* Conversations Table */}
      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">Recent Conversations</h2>
        </div>
        
        {loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        ) : (
          <Table
            data={filteredConversations}
            columns={columns}
            emptyMessage="No conversations found for the selected filters."
          />
        )}
      </div>
    </div>
  );
};

export default MonitoringPage; 