'use client';

import React, { useState, useEffect } from 'react';
import { Plus, Trash2, Users, Search, UserPlus } from 'lucide-react';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import Table from '@/components/ui/Table';
import Modal from '@/components/ui/Modal';
import { api } from '@/lib/api';
// import { useAuthStore } from '@/store/auth';
import { Member, Trainer } from '@/types';
import { formatDate, getStatusColor } from '@/lib/utils';

const MembersPage: React.FC = () => {
  const [members, setMembers] = useState<Member[]>([]);
  const [trainers, setTrainers] = useState<Trainer[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  
  // Modal states
  const [assignModal, setAssignModal] = useState<{
    isOpen: boolean;
    member: Member | null;
  }>({
    isOpen: false,
    member: null
  });

  const [deleteModal, setDeleteModal] = useState<{
    isOpen: boolean;
    member: Member | null;
  }>({
    isOpen: false,
    member: null
  });

  const [sortKey, setSortKey] = useState<keyof Member>('name');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [selectedTrainers, setSelectedTrainers] = useState<number[]>([]);

  // const { user } = useAuthStore();

  useEffect(() => {
    fetchMembers();
    fetchTrainers();
  }, []);

  const fetchMembers = async () => {
    try {
      setLoading(true);
      const response = await api.members.getAll();
      setMembers(response.data);
    } catch (error) {
      console.error('Error fetching members:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchTrainers = async () => {
    try {
      const response = await api.trainers.getAll();
      setTrainers(response.data);
    } catch (error) {
      console.error('Error fetching trainers:', error);
    }
  };

  const handleSort = (key: keyof Member) => {
    if (sortKey === key) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortKey(key);
      setSortDirection('asc');
    }
  };

  const handleDelete = async (member: Member) => {
    try {
      await api.members.delete(member.id);
      setMembers(members.filter(m => m.id !== member.id));
      setDeleteModal({ isOpen: false, member: null });
    } catch (error) {
      console.error('Error deleting member:', error);
    }
  };

  const handleAssignTrainers = async () => {
    if (!assignModal.member || selectedTrainers.length === 0) return;

    try {
      // For each selected trainer, make an assignment call
      await Promise.all(
        selectedTrainers.map(trainerId => 
          api.trainers.assign(trainerId, [assignModal.member!.id])
        )
      );
      
      setAssignModal({ isOpen: false, member: null });
      setSelectedTrainers([]);
      fetchMembers(); // Refresh to show updated assignments
    } catch (error) {
      console.error('Error assigning trainers:', error);
    }
  };

  // Filter and sort members
  const filteredMembers = members.filter(member => {
    const matchesSearch = member.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         member.email.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || 
                         (statusFilter === 'active' && member.isActive) ||
                         (statusFilter === 'inactive' && !member.isActive);
    return matchesSearch && matchesStatus;
  });

  const sortedMembers = [...filteredMembers].sort((a, b) => {
    const aValue = a[sortKey];
    const bValue = b[sortKey];
    
    if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
    if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
    return 0;
  });

  const columns = [
    {
      key: 'name' as keyof Member,
      label: 'Member',
      sortable: true,
      render: (value: string, row: Member) => (
        <div className="flex items-center">
          <div className="h-8 w-8 rounded-full bg-green-100 flex items-center justify-center mr-3">
            <span className="text-sm font-medium text-green-600">
              {value.charAt(0).toUpperCase()}
            </span>
          </div>
          <div>
            <div className="font-medium text-gray-900">{value}</div>
            <div className="text-sm text-gray-500">{row.email}</div>
          </div>
        </div>
      )
    },
    {
      key: 'isActive' as keyof Member,
      label: 'Status',
      sortable: true,
      render: (value: boolean) => (
        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
          value ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
        }`}>
          {value ? 'Active' : 'Inactive'}
        </span>
      )
    },
    {
      key: 'lastLoginAt' as keyof Member,
      label: 'Last Login',
      sortable: true,
      render: (value: string) => value ? formatDate(value) : 'Never'
    },
    {
      key: 'created_at' as keyof Member,
      label: 'Joined',
      sortable: true,
      render: (value: string) => formatDate(value)
    },
    {
      key: 'id' as keyof Member,
      label: 'Actions',
      render: (value: number, row: Member) => (
        <div className="flex space-x-2">
          <Button 
            variant="ghost" 
            size="sm"
            onClick={() => setAssignModal({ isOpen: true, member: row })}
          >
            <UserPlus className="h-4 w-4" />
          </Button>
          <Button 
            variant="ghost" 
            size="sm"
            onClick={() => setDeleteModal({ isOpen: true, member: row })}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      )
    }
  ];

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Members</h1>
          <p className="text-gray-600">Manage platform members and trainer assignments</p>
        </div>
        <Button>
          <Plus className="h-4 w-4 mr-2" />
          Add Member
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <Users className="h-8 w-8 text-blue-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Members</p>
              <p className="text-2xl font-bold text-gray-900">{members.length}</p>
            </div>
          </div>
        </div>
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <Users className="h-8 w-8 text-green-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Active Members</p>
              <p className="text-2xl font-bold text-gray-900">
                {members.filter(m => m.isActive).length}
              </p>
            </div>
          </div>
        </div>
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <Users className="h-8 w-8 text-orange-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">New This Month</p>
              <p className="text-2xl font-bold text-gray-900">
                {members.filter(m => {
                  const created = new Date(m.created_at);
                  const now = new Date();
                  return created.getMonth() === now.getMonth() && 
                         created.getFullYear() === now.getFullYear();
                }).length}
              </p>
            </div>
          </div>
        </div>
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <Users className="h-8 w-8 text-purple-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Recent Logins</p>
              <p className="text-2xl font-bold text-gray-900">
                {members.filter(m => {
                  if (!m.lastLoginAt) return false;
                  const lastLogin = new Date(m.lastLoginAt);
                  const weekAgo = new Date();
                  weekAgo.setDate(weekAgo.getDate() - 7);
                  return lastLogin > weekAgo;
                }).length}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1">
            <Input
              placeholder="Search members..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              leftIcon={<Search className="h-4 w-4" />}
            />
          </div>
          <div className="md:w-48">
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
            >
              <option value="all">All Status</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
            </select>
          </div>
        </div>
      </div>

      {/* Members Table */}
      <div className="bg-white rounded-lg shadow">
        <Table
          data={sortedMembers}
          columns={columns}
          sortKey={sortKey}
          sortDirection={sortDirection}
          onSort={handleSort}
          emptyMessage="No members found. Add members to get started."
        />
      </div>

      {/* Assign Trainers Modal */}
      <Modal
        isOpen={assignModal.isOpen}
        onClose={() => {
          setAssignModal({ isOpen: false, member: null });
          setSelectedTrainers([]);
        }}
        title="Assign Trainers"
        size="md"
      >
        <div className="space-y-4">
          <p className="text-gray-600">
            Select trainers to assign to {assignModal.member?.name}:
          </p>
          
          <div className="space-y-2 max-h-64 overflow-y-auto">
            {trainers.map(trainer => (
              <div key={trainer.id} className="flex items-center">
                <input
                  type="checkbox"
                  id={`trainer-${trainer.id}`}
                  checked={selectedTrainers.includes(trainer.id)}
                  onChange={(e) => {
                    if (e.target.checked) {
                      setSelectedTrainers([...selectedTrainers, trainer.id]);
                    } else {
                      setSelectedTrainers(selectedTrainers.filter(id => id !== trainer.id));
                    }
                  }}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor={`trainer-${trainer.id}`} className="ml-3 flex items-center cursor-pointer">
                  <div>
                    <div className="text-sm font-medium text-gray-900">{trainer.name}</div>
                    <div className="text-sm text-gray-500">{trainer.description}</div>
                  </div>
                  <span className={`ml-auto px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(trainer.status)}`}>
                    {trainer.status}
                  </span>
                </label>
              </div>
            ))}
          </div>

          <div className="flex justify-end space-x-3">
            <Button
              variant="outline"
              onClick={() => {
                setAssignModal({ isOpen: false, member: null });
                setSelectedTrainers([]);
              }}
            >
              Cancel
            </Button>
            <Button
              onClick={handleAssignTrainers}
              disabled={selectedTrainers.length === 0}
            >
              Assign {selectedTrainers.length} Trainer{selectedTrainers.length !== 1 ? 's' : ''}
            </Button>
          </div>
        </div>
      </Modal>

      {/* Delete Modal */}
      <Modal
        isOpen={deleteModal.isOpen}
        onClose={() => setDeleteModal({ isOpen: false, member: null })}
        title="Delete Member"
        size="sm"
      >
        <div className="space-y-4">
          <p className="text-gray-600">
            Are you sure you want to delete member &quot;{deleteModal.member?.name}&quot;? 
            This action cannot be undone and will remove all associated data.
          </p>
          <div className="flex justify-end space-x-3">
            <Button
              variant="outline"
              onClick={() => setDeleteModal({ isOpen: false, member: null })}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={() => deleteModal.member && handleDelete(deleteModal.member)}
            >
              Delete
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default MembersPage; 